{"name": "pokecardmaker", "version": "0.1.0", "private": true, "scripts": {"create:data": "node ./scripts/createCardImgPathArray.js", "optimize:images": "node ./scripts/optimizeImages.js", "prestart": "npm run create:data", "start": "next dev", "dev": "npm run start", "prebuild": "npm run create:data", "build": "next build", "postbuild": "next-sitemap && next export", "lint": "eslint --ext .ts,.tsx src", "lint:fix": "eslint --ext .ts,.tsx src --fix", "typecheck": "npx tsc"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@emotion/cache": "^11.7.1", "@emotion/react": "^11.8.2", "@emotion/server": "^11.4.0", "@emotion/styled": "^11.8.1", "@mui/icons-material": "^5.4.4", "@mui/material": "^5.4.4", "@mui/styled-engine": "^5.7.0", "@mui/system": "^5.7.0", "compress-images": "^2.0.5", "cookies-next": "^2.1.1", "fs-extra": "^10.1.0", "html-react-parser": "^3.0.1", "html-to-image": "^1.9.0", "lodash.merge": "^4.6.2", "mui-color": "^2.0.0-beta.2", "nanoid": "^4.0.0", "next": "^12.1.4", "next-sitemap": "^3.1.21", "react": "^17.0.2", "react-beautiful-dnd": "^13.1.1", "react-device-detect": "^2.2.2", "react-dom": "^17.0.2", "react-easy-crop": "^4.4.0", "react-use": "^17.4.0", "typescript": "^4.6.2", "web-vitals": "^2.1.4", "zustand": "^4.1.4"}, "devDependencies": {"@next/bundle-analyzer": "^12.1.4", "@next/eslint-plugin-next": "^12.1.4", "@types/fs-extra": "^9.0.13", "@types/lodash.merge": "^4.6.7", "@types/node": "^16.11.26", "@types/react": "^17.0.39", "@types/react-beautiful-dnd": "^13.1.2", "@typescript-eslint/eslint-plugin": "^4.11.1", "eslint": "^7.11.0", "eslint-config-airbnb": "^19.0.1", "eslint-config-airbnb-typescript": "^12.0.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-react": "^7.22.0", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unused-imports": "^1.1.1", "fs": "^0.0.1-security", "pngquant-bin": "^6.0.1", "prettier": "^2.5.0"}}