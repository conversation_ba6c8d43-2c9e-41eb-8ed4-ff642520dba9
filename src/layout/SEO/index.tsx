import Head from 'next/head';
import { FC, useMemo } from 'react';
import { useRouter } from 'next/router';

interface SEOProps {
  description: string;
  title?: string;
  siteTitle?: string;
  fullTitle?: string;
  image?: string;
  noindex?: boolean;
}

const SEO: FC<SEOProps> = ({
  description,
  title,
  siteTitle = 'Pokecardmaker.org',
  fullTitle,
  image = 'https://pokecardmaker.org/assets/images/metaImage.png',
  noindex = false,
}) => {
  const router = useRouter();
  const finalTitle = useMemo<string>(
    () => fullTitle ?? `${title} | ${siteTitle}`,
    [fullTitle, title, siteTitle],
  );

  const canonicalUrl = `https://pokecardmaker.org${router.asPath}`;

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{finalTitle}</title>
      <meta name="description" content={description} />
      <meta name="author" content="Pokecardmaker.org" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />

      {/* Canonical URL */}
      <link rel="canonical" href={canonicalUrl} />

      {/* Robots */}
      {noindex ? (
        <meta name="robots" content="noindex, nofollow" />
      ) : (
        <meta
          name="robots"
          content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1"
        />
      )}

      {/* Open Graph Tags */}
      <meta property="og:type" content="website" />
      <meta property="og:title" content={finalTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:site_name" content={siteTitle} />
      <meta property="og:url" content={canonicalUrl} />
      <meta property="og:image" content={image} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta
        property="og:image:alt"
        content="Pokemon Card Maker - Create Custom Pokemon Cards"
      />
      <meta property="og:locale" content="en_US" />

      {/* Twitter Card Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={finalTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
      <meta
        name="twitter:image:alt"
        content="Pokemon Card Maker - Create Custom Pokemon Cards"
      />
      <meta name="twitter:site" content="@pokecardmaker" />
      <meta name="twitter:creator" content="@pokecardmaker" />

      {/* Additional Meta Tags */}
      <meta name="theme-color" content="#d35337" />
      <meta name="msapplication-TileColor" content="#d35337" />
      <meta name="application-name" content="Pokemon Card Maker" />
      <meta name="apple-mobile-web-app-title" content="Pokemon Card Maker" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />

      {/* Language */}
      <meta httpEquiv="content-language" content="en" />
    </Head>
  );
};

export default SEO;
