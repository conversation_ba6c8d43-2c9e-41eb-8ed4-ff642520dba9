import { Box, Link, Paper, Typography } from '@mui/material';
import { FC } from 'react';
import NextLink from 'next/link';
import Routes from '@routes';

const Footer: FC = () => (
  <Paper
    component="footer"
    sx={{
      p: [8, undefined, 2],
      borderRadius: 0,
    }}
  >
    <Box
      sx={{
        display: 'flex',
        flexDirection: ['column', 'row'],
        justifyContent: 'center',
        alignItems: 'center',
        gap: { xs: 1.5, sm: 2 },
        textAlign: 'center',
      }}
    >
      <Typography
        variant="body1"
        sx={{
          fontSize: { xs: '0.9rem', sm: '1rem' },
          fontWeight: 500,
        }}
      >
        © {new Date().getFullYear()} Pokecardmaker.org
      </Typography>

      <Box
        sx={{
          display: 'flex',
          gap: { xs: 1.5, sm: 2 },
          flexWrap: 'wrap',
          justifyContent: 'center',
        }}
      >
        <NextLink href={Routes.PrivacyPolicy} passHref>
          <Link
            color="primary"
            sx={{
              fontSize: { xs: '0.85rem', sm: '0.9rem' },
              fontWeight: 500,
              textDecoration: 'none',
              '&:hover': {
                textDecoration: 'underline',
                opacity: 0.8,
              },
            }}
          >
            Privacy Policy
          </Link>
        </NextLink>

        <NextLink href={Routes.TermsOfService} passHref>
          <Link
            color="primary"
            sx={{
              fontSize: { xs: '0.85rem', sm: '0.9rem' },
              fontWeight: 500,
              textDecoration: 'none',
              '&:hover': {
                textDecoration: 'underline',
                opacity: 0.8,
              },
            }}
          >
            Terms of Service
          </Link>
        </NextLink>
      </Box>
    </Box>
  </Paper>
);

export default Footer;
