import { App<PERSON><PERSON>, <PERSON><PERSON>, Fab, ListItemButton, styled } from '@mui/material';

export const InvisibleHeading = styled('h1')`
  visibility: hidden;
  position: absolute;
  top: 0;
  left: 0;
`;

export const DefaultAppBar = styled(AppBar)`
  background: ${({ theme }) => theme.palette.primary.main};
`;

export const NavItems = styled('nav')`
  display: flex;
  margin-left: ${({ theme }) => theme.spacing(2)};
`;

export const NavItem = styled(Button)`
  padding: ${({ theme }) => theme.spacing(0.75, 4)};
`;

export const Spacer = styled('div')`
  padding: ${({ theme }) => theme.spacing(1)};
`;

// Mobile header //

export const HamburgerFab = styled(Fab)`
  z-index: 100;
  position: fixed;
  top: ${({ theme }) => theme.spacing(1)};
  right: ${({ theme }) => theme.spacing(2)};
  border-radius: ${({ theme }) => theme.spacing(1)};
  background: ${({ theme }) => theme.palette.primary.main};
  width: 48px;
  height: 48px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  &:hover {
    background: ${({ theme }) => theme.palette.primary.dark};
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }
`;

export const PrimaryListItem = styled(ListItemButton)`
  background: ${({ theme }) => theme.palette.primary.main};
`;
