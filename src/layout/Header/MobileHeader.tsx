import { FC, useState } from 'react';
import HamburgerIcon from '@mui/icons-material/Menu';
import {
  AppBar,
  Box,
  List,
  ListItemButton,
  ListItemText,
  SwipeableDrawer,
  Typography,
} from '@mui/material';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { HamburgerFab } from './styles';

const MobileHeader: FC = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const router = useRouter();

  const scrollToSection = (sectionId: string) => {
    setMenuOpen(false);
    setTimeout(() => {
      const element = document.getElementById(sectionId);
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      }
    }, 100);
  };

  // Only show navigation on the homepage
  if (router.pathname !== '/') {
    return null;
  }

  return (
    <>
      <HamburgerFab color="primary" onClick={() => setMenuOpen(true)}>
        <HamburgerIcon />
      </HamburgerFab>
      <SwipeableDrawer
        anchor="top"
        open={menuOpen}
        onClose={() => setMenuOpen(false)}
        onOpen={() => setMenuOpen(true)}
      >
        <Box component="nav" sx={{ width: 'auto' }} role="presentation">
          <Box component={Link} textTransform="uppercase" href="/">
            <AppBar position="relative" color="primary" enableColorOnDark>
              <Box px={2}>
                <Typography variant="h4" fontWeight="bold">
                  Pokecardmaker.org
                </Typography>
              </Box>
            </AppBar>
          </Box>
          <List sx={{ py: 0 }}>
            <ListItemButton onClick={() => scrollToSection('card-creator')}>
              <ListItemText primary="Creator" />
            </ListItemButton>
            <ListItemButton onClick={() => scrollToSection('features')}>
              <ListItemText primary="Features" />
            </ListItemButton>
            <ListItemButton onClick={() => scrollToSection('how-to-use')}>
              <ListItemText primary="How to Use" />
            </ListItemButton>
            <ListItemButton onClick={() => scrollToSection('faq')}>
              <ListItemText primary="FAQ" />
            </ListItemButton>
            <ListItemButton onClick={() => scrollToSection('about')}>
              <ListItemText primary="About" />
            </ListItemButton>
          </List>
        </Box>
      </SwipeableDrawer>
    </>
  );
};

export default MobileHeader;
