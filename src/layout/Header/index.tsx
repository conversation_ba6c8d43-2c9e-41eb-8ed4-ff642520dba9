import ThemeToggle from '@components/ThemeToggle';
import { Box, Link, Toolbar, Typography } from '@mui/material';
import Routes from '@routes';
import NextLink from 'next/link';
import { FC } from 'react';
import DesktopHeader from './DesktopHeader';
import MobileHeader from './MobileHeader';
import { DefaultAppBar } from './styles';

const Header: FC = () => {
  return (
    <Box component="header">
      <DefaultAppBar position="relative" color="primary">
        <Toolbar sx={{ justifyContent: 'space-between' }}>
          <NextLink href={Routes.Home} passHref>
            <Typography
              variant="h6"
              component={Link}
              color="white"
              sx={{
                fontWeight: 'bold',
                fontSize: { xs: '1.1rem', sm: '1.25rem', md: '1.3rem' },
                textDecoration: 'none',
                '&:hover': {
                  opacity: 0.9,
                },
              }}
            >
              Pokecardmaker.org
            </Typography>
          </NextLink>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box sx={{ display: { xs: 'none', sm: 'block' } }}>
              <DesktopHeader />
            </Box>
            <ThemeToggle />
          </Box>
        </Toolbar>
      </DefaultAppBar>
      <Box sx={{ display: { xs: 'block', sm: 'none' } }}>
        <MobileHeader />
      </Box>
    </Box>
  );
};

export default Header;
