import { FC } from 'react';
import { Box, Button } from '@mui/material';
import { useRouter } from 'next/router';

const DesktopHeader: FC = () => {
  const router = useRouter();

  // Only show navigation on the homepage
  if (router.pathname !== '/') {
    return null;
  }

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  };

  return (
    <Box sx={{ display: 'flex', gap: 1, ml: 2 }}>
      <Button
        color="inherit"
        onClick={() => scrollToSection('card-creator')}
        sx={{
          color: 'white',
          '&:hover': { backgroundColor: 'rgba(255, 255, 255, 0.1)' },
        }}
      >
        Creator
      </Button>
      <Button
        color="inherit"
        onClick={() => scrollToSection('features')}
        sx={{
          color: 'white',
          '&:hover': { backgroundColor: 'rgba(255, 255, 255, 0.1)' },
        }}
      >
        Features
      </Button>
      <Button
        color="inherit"
        onClick={() => scrollToSection('how-to-use')}
        sx={{
          color: 'white',
          '&:hover': { backgroundColor: 'rgba(255, 255, 255, 0.1)' },
        }}
      >
        How to Use
      </Button>
      <Button
        color="inherit"
        onClick={() => scrollToSection('faq')}
        sx={{
          color: 'white',
          '&:hover': { backgroundColor: 'rgba(255, 255, 255, 0.1)' },
        }}
      >
        FAQ
      </Button>
      <Button
        color="inherit"
        onClick={() => scrollToSection('about')}
        sx={{
          color: 'white',
          '&:hover': { backgroundColor: 'rgba(255, 255, 255, 0.1)' },
        }}
      >
        About
      </Button>
    </Box>
  );
};

export default DesktopHeader;
