import { useCardLogic } from '@cardEditor/cardLogic';
import { useCardOptions } from '@cardEditor/cardOptions';
import { useCardPlacements } from '@cardEditor/cardStyles/hooks';
import { FC, memo } from 'react';
import { Img } from './styles';

const PrevolveImg: FC = () => {
  const { prevolveImg: placement } = useCardPlacements(['prevolveImg']);
  const { hasPrevolveImg } = useCardLogic(['hasPrevolveImg']);
  const { prevolveImgSrc } = useCardOptions(['prevolveImgSrc']);

  if (!hasPrevolveImg || !prevolveImgSrc) return null;

  return <Img $url={prevolveImgSrc} placement={placement} />;
};

export default memo(PrevolveImg);
