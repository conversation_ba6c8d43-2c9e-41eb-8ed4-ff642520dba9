import { FC } from 'react';
import { Box, Typography } from '@mui/material';
import CardOptionsForm from '@cardEditor/cardOptions/components/CardOptionsForm';
import CardDisplay from '@cardEditor/cardStyles/components/CardDisplay';
import { SEO } from '@layout';
import CardDownloader from '@cardEditor/cardOptions/components/atoms/CardDownloader';
import StructuredData from '@components/StructuredData';
import Features from '@components/Features';
import HowToUse from '@components/HowToUse';
import FAQ from '@components/FAQ';
import About from '@components/About';
import CTA from '@components/CTA';
import { CardWrapper, Wrapper } from './styles';

const Home: FC = () => (
  <>
    <SEO
      fullTitle="Pokemon Card Maker - Create Custom Cards Free Online"
      description="Create professional Pokemon cards with our free Pokemon card maker. Design Sword & Shield, Scarlet & Violet cards with authentic templates. Start now!"
    />
    <StructuredData type="WebApplication" />

    {/* Hero Section with H1 */}
    <Box
      id="hero"
      component="section"
      sx={{
        textAlign: 'center',
        mb: 6,
        py: { xs: 4, md: 6 },
        background: 'transparent',
        borderRadius: 2,
        border: '1px solid',
        borderColor: 'primary.light',
        mx: { xs: -3, md: 0 },
      }}
    >
      <Typography
        variant="h1"
        component="h1"
        sx={{
          fontSize: { xs: '2.2rem', md: '3rem' },
          fontWeight: 'bold',
          mb: 3,
          color: 'primary.main',
          lineHeight: 1.2,
        }}
      >
        Pokemon Card Maker
      </Typography>
      <Typography
        variant="h2"
        component="h2"
        sx={{
          fontSize: { xs: '1.1rem', md: '1.3rem' },
          fontWeight: 'normal',
          mb: 2,
          color: 'text.primary',
          maxWidth: 600,
          mx: 'auto',
          lineHeight: 1.5,
        }}
      >
        Create Custom Pokemon Cards Online
      </Typography>
      <Typography
        variant="body1"
        sx={{
          fontSize: { xs: '1rem', md: '1.1rem' },
          color: 'text.secondary',
          maxWidth: 700,
          mx: 'auto',
          lineHeight: 1.6,
        }}
      >
        Design professional Pokemon cards with our free, easy-to-use Pokemon
        card maker. The best Pokemon card maker tool for creating custom cards
        online.
      </Typography>
    </Box>

    {/* Main Content Area */}
    <Box component="main" role="main">
      <Box
        id="card-editor"
        sx={{
          mb: 8,
          p: { xs: 2, md: 4 },
          backgroundColor: 'background.paper',
          borderRadius: 3,
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
          border: '1px solid',
          borderColor: 'divider',
        }}
      >
        <Typography
          variant="h3"
          component="h3"
          align="center"
          sx={{
            mb: 4,
            fontWeight: 'bold',
            color: 'primary.main',
            fontSize: { xs: '1.8rem', md: '2.2rem' },
          }}
        >
          Start Creating Your Custom Pokemon Card
        </Typography>
        <Wrapper>
          <Box component="section" aria-label="Card customization options">
            <CardOptionsForm />
          </Box>
          <CardWrapper>
            <Box component="section" aria-label="Card preview and download">
              <CardDisplay />
              <Box sx={{ mt: 3 }}>
                <CardDownloader />
              </Box>
            </Box>
          </CardWrapper>
        </Wrapper>
      </Box>

      {/* Features Section */}
      <Box
        id="features"
        sx={{
          mb: 8,
          py: { xs: 4, md: 6 },
          backgroundColor: 'background.default',
        }}
      >
        <Features />
      </Box>

      {/* How to Use Section */}
      <Box
        id="how-to-use"
        sx={{
          mb: 8,
          py: { xs: 4, md: 6 },
          backgroundColor: 'background.paper',
        }}
      >
        <HowToUse />
      </Box>

      {/* FAQ Section */}
      <Box
        id="faq"
        sx={{
          mb: 8,
          py: { xs: 4, md: 6 },
          backgroundColor: 'background.default',
        }}
      >
        <FAQ />
      </Box>

      {/* About Section */}
      <Box
        id="about"
        sx={{
          mb: 8,
          py: { xs: 4, md: 6 },
          backgroundColor: 'background.paper',
        }}
      >
        <About />
      </Box>

      {/* Call to Action Section */}
      <CTA />
    </Box>
  </>
);

export default Home;
