import { FC } from 'react';
import { Box, Container, Paper, Typography } from '@mui/material';
import { SEO } from '@layout';

const PrivacyPolicyPage: FC = () => (
  <>
    <SEO
      title="Privacy Policy"
      description="Privacy Policy for Pokecardmaker.org - Learn how we protect your privacy while using our Pokemon card maker tool."
    />
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper
        sx={{
          p: { xs: 3, md: 5 },
          mt: 2,
          borderRadius: 3,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          border: '1px solid',
          borderColor: 'divider',
        }}
      >
        <Typography
          variant="h1"
          component="h1"
          sx={{
            mb: 4,
            fontSize: { xs: '2rem', md: '2.5rem' },
            fontWeight: 'bold',
            color: 'primary.main',
            textAlign: 'center',
          }}
        >
          Privacy Policy
        </Typography>

        <Typography variant="body1" sx={{ mb: 3 }}>
          <strong>Last updated:</strong> {new Date().toLocaleDateString()}
        </Typography>

        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h2"
            component="h2"
            sx={{ mb: 2, fontSize: '1.5rem', fontWeight: 'bold' }}
          >
            1. Information We Collect
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Pokecardmaker.org is committed to protecting your privacy. We
            collect minimal information to provide our services:
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            • <strong>Analytics Data:</strong> We use Google Analytics to
            understand how visitors use our website. This includes anonymous
            data such as page views, session duration, and general geographic
            location.
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            • <strong>Local Storage:</strong> Your card designs and preferences
            are stored locally in your browser and are not transmitted to our
            servers.
          </Typography>
        </Box>

        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h2"
            component="h2"
            sx={{ mb: 2, fontSize: '1.5rem', fontWeight: 'bold' }}
          >
            2. How We Use Your Information
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            We use the collected information to:
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            • Improve our website and user experience
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            • Understand usage patterns and popular features
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            • Ensure our service is working properly
          </Typography>
        </Box>

        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h2"
            component="h2"
            sx={{ mb: 2, fontSize: '1.5rem', fontWeight: 'bold' }}
          >
            3. Data Storage and Security
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            • We do not store your personal card designs on our servers
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            • All card creation happens locally in your browser
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            • We do not collect or store personal information such as names,
            email addresses, or payment information
          </Typography>
        </Box>

        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h2"
            component="h2"
            sx={{ mb: 2, fontSize: '1.5rem', fontWeight: 'bold' }}
          >
            4. Third-Party Services
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            We use Google Analytics to analyze website usage. Google Analytics
            may collect and process data according to their own privacy policy.
            You can opt out of Google Analytics by using the Google Analytics
            Opt-out Browser Add-on.
          </Typography>
        </Box>

        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h2"
            component="h2"
            sx={{ mb: 2, fontSize: '1.5rem', fontWeight: 'bold' }}
          >
            5. Cookies
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            We use cookies and similar technologies to:
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            • Remember your preferences and settings
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            • Provide analytics through Google Analytics
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            You can control cookies through your browser settings.
          </Typography>
        </Box>

        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h2"
            component="h2"
            sx={{ mb: 2, fontSize: '1.5rem', fontWeight: 'bold' }}
          >
            6. Your Rights
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Since we don&apos;t collect personal information, there is no
            personal data to access, modify, or delete. Your card designs are
            stored locally in your browser and can be cleared by clearing your
            browser&apos;s local storage.
          </Typography>
        </Box>

        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h2"
            component="h2"
            sx={{ mb: 2, fontSize: '1.5rem', fontWeight: 'bold' }}
          >
            7. Changes to This Policy
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            We may update this privacy policy from time to time. Any changes
            will be posted on this page with an updated date.
          </Typography>
        </Box>

        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h2"
            component="h2"
            sx={{ mb: 2, fontSize: '1.5rem', fontWeight: 'bold' }}
          >
            8. Contact Us
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            If you have any questions about this Privacy Policy, please contact
            us at:
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            <strong>Email:</strong> <EMAIL>
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            <strong>Website:</strong> pokecardmaker.org
          </Typography>
        </Box>
      </Paper>
    </Container>
  </>
);

export default PrivacyPolicyPage;
