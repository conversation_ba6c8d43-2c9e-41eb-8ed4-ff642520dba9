import { FC } from 'react';
import { Box, Container, Paper, Typography } from '@mui/material';
import { SEO } from '@layout';

const TermsOfServicePage: FC = () => (
  <>
    <SEO
      title="Terms of Service"
      description="Terms of Service for Pokecardmaker.org - Read our terms and conditions for using our Pokemon card maker tool."
    />
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper
        sx={{
          p: { xs: 3, md: 5 },
          mt: 2,
          borderRadius: 3,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          border: '1px solid',
          borderColor: 'divider',
        }}
      >
        <Typography
          variant="h1"
          component="h1"
          sx={{
            mb: 4,
            fontSize: { xs: '2rem', md: '2.5rem' },
            fontWeight: 'bold',
            color: 'primary.main',
            textAlign: 'center',
          }}
        >
          Terms of Service
        </Typography>

        <Typography variant="body1" sx={{ mb: 3 }}>
          <strong>Last updated:</strong> {new Date().toLocaleDateString()}
        </Typography>

        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h2"
            component="h2"
            sx={{ mb: 2, fontSize: '1.5rem', fontWeight: 'bold' }}
          >
            1. Acceptance of Terms
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            By accessing and using Pokecardmaker.org, you accept and agree to be
            bound by the terms and provision of this agreement.
          </Typography>
        </Box>

        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h2"
            component="h2"
            sx={{ mb: 2, fontSize: '1.5rem', fontWeight: 'bold' }}
          >
            2. Description of Service
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Pokecardmaker.org provides a free online tool for creating custom
            Pokemon-style trading cards. The service is provided &quot;as
            is&quot; without any warranties.
          </Typography>
        </Box>

        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h2"
            component="h2"
            sx={{ mb: 2, fontSize: '1.5rem', fontWeight: 'bold' }}
          >
            3. User Responsibilities
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Users are responsible for:
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            • Using the service in compliance with all applicable laws
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            • Not using copyrighted images without proper authorization
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            • Not creating content that is offensive, harmful, or inappropriate
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            • Understanding that created cards are for personal, non-commercial
            use only
          </Typography>
        </Box>

        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h2"
            component="h2"
            sx={{ mb: 2, fontSize: '1.5rem', fontWeight: 'bold' }}
          >
            4. Intellectual Property
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            • Pokemon and related trademarks are owned by Nintendo, Game Freak,
            and Creatures Inc.
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            • This tool is for fan use and educational purposes only
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            • Users retain ownership of their original content and images
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            • Cards created should not be used for commercial purposes
          </Typography>
        </Box>

        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h2"
            component="h2"
            sx={{ mb: 2, fontSize: '1.5rem', fontWeight: 'bold' }}
          >
            5. Disclaimer of Warranties
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            The service is provided &quot;as is&quot; without any warranties,
            express or implied. We do not guarantee that the service will be
            uninterrupted, error-free, or completely secure.
          </Typography>
        </Box>

        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h2"
            component="h2"
            sx={{ mb: 2, fontSize: '1.5rem', fontWeight: 'bold' }}
          >
            6. Limitation of Liability
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Pokecardmaker.org shall not be liable for any direct, indirect,
            incidental, special, or consequential damages resulting from the use
            or inability to use the service.
          </Typography>
        </Box>

        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h2"
            component="h2"
            sx={{ mb: 2, fontSize: '1.5rem', fontWeight: 'bold' }}
          >
            7. Privacy
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Your privacy is important to us. Please review our Privacy Policy,
            which also governs your use of the service.
          </Typography>
        </Box>

        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h2"
            component="h2"
            sx={{ mb: 2, fontSize: '1.5rem', fontWeight: 'bold' }}
          >
            8. Modifications to Terms
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            We reserve the right to modify these terms at any time. Changes will
            be effective immediately upon posting on this page.
          </Typography>
        </Box>

        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h2"
            component="h2"
            sx={{ mb: 2, fontSize: '1.5rem', fontWeight: 'bold' }}
          >
            9. Termination
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            We may terminate or suspend access to our service immediately,
            without prior notice, for any reason whatsoever.
          </Typography>
        </Box>

        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h2"
            component="h2"
            sx={{ mb: 2, fontSize: '1.5rem', fontWeight: 'bold' }}
          >
            10. Contact Information
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            If you have any questions about these Terms of Service, please
            contact us at:
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            <strong>Email:</strong> <EMAIL>
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            <strong>Website:</strong> pokecardmaker.org
          </Typography>
        </Box>
      </Paper>
    </Container>
  </>
);

export default TermsOfServicePage;
