import { Forward as ForwardIcon } from '@mui/icons-material';
import { SEO } from '@layout';
import { Button, Typography } from '@mui/material';
import { Box } from '@mui/system';
import Image from 'next/image';
import { FC } from 'react';
import notFoundImage from '@assets/images/psyduck404.png';
import NextLink from 'next/link';
import Routes from '@routes';

const NotFoundPage: FC = () => (
  <>
    <SEO
      title="Page not found"
      description="The page you're looking for doesn't exist. Return to our Pokemon card maker homepage."
    />
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      gap={4}
      width="fit-content"
      margin="0 auto"
      sx={{
        textAlign: 'center',
        py: { xs: 4, md: 6 },
        px: 2,
      }}
    >
      <Typography
        variant="h1"
        component="h1"
        sx={{
          fontSize: { xs: '2rem', md: '2.5rem' },
          fontWeight: 'bold',
          color: 'primary.main',
          mb: 2,
        }}
      >
        Oh no! Page not found!
      </Typography>
      <Typography
        variant="body1"
        sx={{
          color: 'text.secondary',
          mb: 2,
          maxWidth: 800,
        }}
      >
        The page you&apos;re looking for doesn&apos;t exist. Let&apos;s get you
        back to creating amazing Pokemon cards!
      </Typography>
      <Box sx={{ mb: 2 }}>
        <Image
          src={notFoundImage}
          alt="Confused Psyduck Pokemon character for 404 error page"
        />
      </Box>
      <NextLink href={Routes.Home} passHref>
        <Button
          variant="contained"
          size="large"
          endIcon={<ForwardIcon />}
          sx={{
            px: 4,
            py: 1.5,
            fontSize: '1.1rem',
          }}
        >
          Go to Homepage
        </Button>
      </NextLink>
    </Box>
  </>
);

export default NotFoundPage;
