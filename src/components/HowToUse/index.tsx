import { FC } from 'react';
import {
  <PERSON><PERSON>,
  Box,
  Card,
  Card<PERSON>ontent,
  Container,
  Grid,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>per,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import {
  CloudUpload,
  Download,
  Edit,
  Palette,
  Tune,
  Visibility,
} from '@mui/icons-material';

const HowToUse: FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const steps = [
    {
      icon: <Palette />,
      title: 'Choose Your Template',
      description:
        'Select from Sword & Shield, Scarlet & Violet, or classic Pokemon card designs with authentic styling.',
      details:
        'Browse our collection of professional Pokemon card templates and pick the style that matches your vision.',
    },
    {
      icon: <CloudUpload />,
      title: 'Upload Your Artwork',
      description:
        'Upload your Pokemon artwork or use our built-in options for the perfect card image.',
      details:
        'Support for PNG, JPG, and JPEG formats. High-quality images work best for professional results.',
    },
    {
      icon: <Edit />,
      title: 'Customize Card Details',
      description:
        'Add Pokemon name, HP, attacks, abilities, and stats to bring your card to life.',
      details:
        'Use our intuitive editor to set energy costs, damage amounts, and special effect descriptions.',
    },
    {
      icon: <Tune />,
      title: 'Set Types & Properties',
      description:
        'Configure Pokemon types, weakness, resistance, and retreat cost for balanced gameplay.',
      details:
        'Choose from all Pokemon types and set strategic elements that make your card unique.',
    },
    {
      icon: <Visibility />,
      title: 'Preview Your Card',
      description:
        'See your custom Pokemon card in real-time with our live preview feature.',
      details:
        'Make adjustments and see changes instantly before finalizing your design.',
    },
    {
      icon: <Download />,
      title: 'Download & Share',
      description:
        'Export your finished Pokemon card in high quality, perfect for printing or sharing.',
      details:
        'Get print-ready files that maintain crisp quality for physical cards or digital sharing.',
    },
  ];

  return (
    <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
      <Box component="section">
        {/* Section Header */}
        <Box sx={{ textAlign: 'center', mb: 6 }}>
          <Typography
            variant="h2"
            component="h2"
            sx={{
              fontSize: { xs: '2rem', md: '2.5rem' },
              fontWeight: 'bold',
              mb: 2,
              color: 'primary.main',
            }}
          >
            How to Create Pokemon Cards
          </Typography>
          <Typography
            variant="h3"
            component="p"
            sx={{
              fontSize: { xs: '1.1rem', md: '1.3rem' },
              color: 'text.secondary',
              maxWidth: 600,
              mx: 'auto',
              lineHeight: 1.6,
            }}
          >
            Follow these simple steps to design professional Pokemon cards with
            our free online Pokemon card maker
          </Typography>
        </Box>

        {/* Desktop Stepper */}
        {!isMobile && (
          <Box sx={{ mb: 6 }}>
            <Stepper
              activeStep={-1}
              alternativeLabel
              sx={{
                '& .MuiStepLabel-label': {
                  fontSize: '0.9rem',
                  fontWeight: 500,
                },
                '& .MuiStepIcon-root': {
                  color: 'primary.main',
                  '&.Mui-active': {
                    color: 'primary.main',
                  },
                  '&.Mui-completed': {
                    color: 'primary.main',
                  },
                },
              }}
            >
              {steps.map((step, index) => (
                <Step key={index}>
                  <StepLabel>{step.title}</StepLabel>
                </Step>
              ))}
            </Stepper>
          </Box>
        )}

        {/* Steps Grid */}
        <Grid container spacing={4}>
          {steps.map((step, index) => (
            <Grid item xs={12} md={6} lg={4} key={index}>
              <Card
                sx={{
                  height: '100%',
                  position: 'relative',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: muiTheme =>
                      muiTheme.palette.mode === 'dark'
                        ? '0 8px 25px rgba(255, 255, 255, 0.15)'
                        : '0 8px 25px rgba(0, 0, 0, 0.15)',
                  },
                  background: 'transparent',
                  border: '1px solid',
                  borderColor: 'divider',
                }}
              >
                <CardContent sx={{ p: 3 }}>
                  {/* Step Number & Icon */}
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      mb: 2,
                    }}
                  >
                    <Avatar
                      sx={{
                        bgcolor: 'primary.main',
                        color: 'white',
                        width: 48,
                        height: 48,
                        mr: 2,
                        fontSize: '1.2rem',
                        fontWeight: 'bold',
                      }}
                    >
                      {index + 1}
                    </Avatar>
                    <Box
                      sx={{
                        color: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                      }}
                    >
                      {step.icon}
                    </Box>
                  </Box>

                  {/* Step Content */}
                  <Typography
                    variant="h6"
                    component="h4"
                    sx={{
                      fontWeight: 'bold',
                      mb: 1,
                      color: 'text.primary',
                    }}
                  >
                    {step.title}
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{
                      mb: 2,
                      color: 'text.secondary',
                      lineHeight: 1.6,
                    }}
                  >
                    {step.description}
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      color: 'text.secondary',
                      fontStyle: 'italic',
                      lineHeight: 1.5,
                    }}
                  >
                    {step.details}
                  </Typography>
                </CardContent>

                {/* Step Connector Line (for larger screens) */}
                {!isMobile &&
                  index < steps.length - 1 &&
                  (index + 1) % 3 !== 0 && (
                    <Box
                      sx={{
                        position: 'absolute',
                        top: '50%',
                        right: -16,
                        width: 32,
                        height: 2,
                        bgcolor: 'primary.light',
                        transform: 'translateY(-50%)',
                        zIndex: 1,
                      }}
                    />
                  )}
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* Call to Action */}
        <Box
          sx={{
            textAlign: 'center',
            mt: 6,
            p: 4,
            background: 'transparent',
            borderRadius: 3,
            border: '1px solid',
            borderColor: 'primary.light',
          }}
        >
          <Typography
            variant="h5"
            component="h4"
            sx={{
              fontWeight: 'bold',
              mb: 2,
              color: 'primary.main',
            }}
          >
            Ready to Create Your Pokemon Card?
          </Typography>
          <Typography
            variant="body1"
            sx={{
              color: 'text.secondary',
              maxWidth: 600,
              mx: 'auto',
              lineHeight: 1.6,
            }}
          >
            Start designing your custom Pokemon cards now with our free online
            Pokemon card maker. No registration required - just jump in and
            create!
          </Typography>
        </Box>
      </Box>
    </Container>
  );
};

export default HowToUse;
