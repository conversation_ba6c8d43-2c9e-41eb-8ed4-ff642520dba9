import { FC, useState } from 'react';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Container,
  Typography,
} from '@mui/material';
import { ExpandMore } from '@mui/icons-material';

interface FAQItem {
  question: string;
  answer: string;
}

const FAQ: FC = () => {
  const [expanded, setExpanded] = useState<string | false>(false);

  const handleChange =
    (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setExpanded(isExpanded ? panel : false);
    };

  const faqItems: FAQItem[] = [
    {
      question: 'Is the Pokemon card maker free to use?',
      answer:
        'Yes! Our Pokemon card maker is completely free to use. You can create, customize, and download as many Pokemon cards as you want without any cost or registration required.',
    },
    {
      question: 'What Pokemon card formats are supported?',
      answer:
        'We support multiple Pokemon card formats including Sword & Shield, Scarlet & Violet, Sun & Moon GX, VMAX, VSTAR, and classic Pokemon card designs. You can choose from various templates to match your preferred style.',
    },
    {
      question: 'Can I upload my own Pokemon artwork?',
      answer:
        'Absolutely! You can upload your own custom Pokemon artwork, fan art, or original designs. We support common image formats like PNG, JPG, and JPEG. Make sure your images are high quality for the best results.',
    },
    {
      question: 'What resolution are the downloaded Pokemon cards?',
      answer:
        'Our Pokemon card maker generates high-resolution cards perfect for printing or digital sharing. The cards are created at print-quality resolution to ensure crisp, professional-looking results.',
    },
    {
      question: 'Can I create Pokemon trainer cards and energy cards?',
      answer:
        'Yes! In addition to Pokemon cards, our tool supports creating trainer cards, supporter cards, and energy cards. You can customize all aspects including artwork, text, and card effects.',
    },
    {
      question: 'Do I need to create an account to use the Pokemon card maker?',
      answer:
        'No account required! You can start creating Pokemon cards immediately without any registration. Your card designs are processed locally in your browser for privacy and convenience.',
    },
    {
      question: 'Can I save my Pokemon card designs?',
      answer:
        'You can download your completed Pokemon cards as image files. We also provide import/export functionality to save and load your card configurations for future editing.',
    },
    {
      question:
        'Are there any copyright concerns with creating custom Pokemon cards?',
      answer:
        "Our tool is designed for personal, educational, and fan use. Custom Pokemon cards created with our tool should not be used for commercial purposes. Always respect Pokemon Company's intellectual property rights.",
    },
    {
      question: 'What browsers work best with the Pokemon card maker?',
      answer:
        'Our Pokemon card maker works on all modern web browsers including Chrome, Firefox, Safari, and Edge. For the best experience, we recommend using the latest version of your preferred browser.',
    },
    {
      question: 'Can I create Pokemon cards on mobile devices?',
      answer:
        'Yes! Our Pokemon card maker is fully responsive and works on mobile devices and tablets. While the desktop experience offers more screen space, you can create great Pokemon cards on any device.',
    },
    {
      question: 'How do I add attacks and abilities to my Pokemon cards?',
      answer:
        'Use our intuitive card editor to add attacks, abilities, and special moves to your Pokemon cards. You can customize energy costs, damage amounts, and effect descriptions to create unique Pokemon abilities.',
    },
    {
      question: 'Can I create holographic or special effect Pokemon cards?',
      answer:
        "Our tool focuses on creating clean, professional Pokemon card designs. While we don't simulate holographic effects in the digital version, the high-quality output is perfect for printing on specialty card stock.",
    },
  ];

  return (
    <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
      <Box component="section">
        {/* Section Header */}
        <Box sx={{ textAlign: 'center', mb: 6 }}>
          <Typography
            variant="h2"
            component="h2"
            sx={{
              fontSize: { xs: '2rem', md: '2.5rem' },
              fontWeight: 'bold',
              mb: 2,
              color: 'primary.main',
            }}
          >
            Frequently Asked Questions
          </Typography>
          <Typography
            variant="h3"
            component="p"
            sx={{
              fontSize: { xs: '1.1rem', md: '1.3rem' },
              color: 'text.secondary',
              maxWidth: 600,
              mx: 'auto',
              lineHeight: 1.6,
            }}
          >
            Everything you need to know about our Pokemon card maker tool
          </Typography>
        </Box>

        <Box sx={{ maxWidth: 900, mx: 'auto' }}>
          {faqItems.map((item, index) => (
            <Accordion
              key={index}
              expanded={expanded === `panel${index}`}
              onChange={handleChange(`panel${index}`)}
              sx={{
                mb: 2,
                borderRadius: 2,
                border: '1px solid',
                borderColor: 'divider',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                '&:before': {
                  display: 'none',
                },
                '&.Mui-expanded': {
                  boxShadow: '0 4px 16px rgba(0, 0, 0, 0.15)',
                },
              }}
            >
              <AccordionSummary
                expandIcon={<ExpandMore />}
                aria-controls={`panel${index}bh-content`}
                id={`panel${index}bh-header`}
                sx={{
                  px: 3,
                  py: 2,
                  '&.Mui-expanded': {
                    borderBottom: '1px solid',
                    borderColor: 'divider',
                  },
                }}
              >
                <Typography
                  variant="h6"
                  component="h4"
                  sx={{
                    fontWeight: 600,
                    color: 'text.primary',
                  }}
                >
                  {item.question}
                </Typography>
              </AccordionSummary>
              <AccordionDetails sx={{ px: 3, py: 2 }}>
                <Typography
                  variant="body1"
                  sx={{
                    lineHeight: 1.7,
                    color: 'text.secondary',
                  }}
                >
                  {item.answer}
                </Typography>
              </AccordionDetails>
            </Accordion>
          ))}
        </Box>
      </Box>
    </Container>
  );
};

export default FAQ;
