import { FC } from 'react';
import {
  Box,
  Card,
  CardContent,
  Container,
  Grid,
  Typography,
} from '@mui/material';
import {
  DeviceHub,
  Download,
  Palette,
  Security,
  Speed,
  Star,
} from '@mui/icons-material';

const Features: FC = () => {
  const features = [
    {
      icon: <Palette color="primary" sx={{ fontSize: 40 }} />,
      title: 'Professional Templates',
      description:
        'Choose from Sword & Shield, Scarlet & Violet, and classic Pokemon card designs with authentic styling and official layouts.',
    },
    {
      icon: <Speed color="primary" sx={{ fontSize: 40 }} />,
      title: 'Easy to Use',
      description:
        'Intuitive interface makes creating custom Pokemon cards simple for beginners and experts alike. No design experience needed.',
    },
    {
      icon: <Download color="primary" sx={{ fontSize: 40 }} />,
      title: 'High-Quality Downloads',
      description:
        'Export your custom Pokemon cards in high resolution, perfect for printing or sharing online with crisp, professional results.',
    },
    {
      icon: <Security color="primary" sx={{ fontSize: 40 }} />,
      title: 'Free & Safe',
      description:
        'Completely free Pokemon card maker with no registration required. Your designs stay private and secure in your browser.',
    },
    {
      icon: <DeviceHub color="primary" sx={{ fontSize: 40 }} />,
      title: 'Cross-Platform',
      description:
        'Works seamlessly on desktop, tablet, and mobile devices. Create Pokemon cards anywhere, anytime with responsive design.',
    },
    {
      icon: <Star color="primary" sx={{ fontSize: 40 }} />,
      title: 'Premium Quality',
      description:
        'Professional-grade output with authentic Pokemon card styling, perfect for collectors, fans, and creative projects.',
    },
  ];

  return (
    <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
      <Box component="section">
        {/* Section Header */}
        <Box sx={{ textAlign: 'center', mb: 6 }}>
          <Typography
            variant="h2"
            component="h2"
            sx={{
              fontSize: { xs: '2rem', md: '2.5rem' },
              fontWeight: 'bold',
              mb: 2,
              color: 'primary.main',
            }}
          >
            Why Choose Our Pokemon Card Maker?
          </Typography>
          <Typography
            variant="h3"
            component="p"
            sx={{
              fontSize: { xs: '1.1rem', md: '1.3rem' },
              color: 'text.secondary',
              maxWidth: 600,
              mx: 'auto',
              lineHeight: 1.6,
            }}
          >
            The most powerful and user-friendly Pokemon card creator with
            professional features and authentic designs
          </Typography>
        </Box>

        {/* Features Grid */}
        <Grid container spacing={4}>
          {features.map((feature, index) => (
            <Grid item xs={12} sm={6} lg={4} key={index}>
              <Card
                sx={{
                  height: '100%',
                  textAlign: 'center',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: theme =>
                      theme.palette.mode === 'dark'
                        ? '0 12px 40px rgba(255, 255, 255, 0.15)'
                        : '0 12px 40px rgba(0, 0, 0, 0.15)',
                  },
                  background: 'transparent',
                  border: '1px solid',
                  borderColor: 'divider',
                  borderRadius: 3,
                }}
              >
                <CardContent sx={{ p: 4 }}>
                  {/* Feature Icon */}
                  <Box
                    sx={{
                      mb: 3,
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: 80,
                      height: 80,
                      mx: 'auto',
                      borderRadius: '50%',
                      background:
                        'linear-gradient(135deg, rgba(211, 83, 55, 0.1) 0%, rgba(211, 83, 55, 0.05) 100%)',
                      border: '2px solid',
                      borderColor: 'primary.light',
                    }}
                  >
                    {feature.icon}
                  </Box>

                  {/* Feature Content */}
                  <Typography
                    variant="h6"
                    component="h4"
                    sx={{
                      mb: 2,
                      fontWeight: 'bold',
                      color: 'text.primary',
                    }}
                  >
                    {feature.title}
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{
                      color: 'text.secondary',
                      lineHeight: 1.6,
                    }}
                  >
                    {feature.description}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* Stats Section */}
        <Box
          sx={{
            mt: 8,
            p: 4,
            background: 'transparent',
            borderRadius: 3,
            border: '1px solid',
            borderColor: 'primary.light',
          }}
        >
          <Grid container spacing={4} sx={{ textAlign: 'center' }}>
            <Grid item xs={12} sm={4}>
              <Typography
                variant="h3"
                component="div"
                sx={{
                  fontWeight: 'bold',
                  color: 'primary.main',
                  mb: 1,
                }}
              >
                100K+
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  color: 'text.secondary',
                  fontWeight: 500,
                }}
              >
                Cards Created
              </Typography>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography
                variant="h3"
                component="div"
                sx={{
                  fontWeight: 'bold',
                  color: 'primary.main',
                  mb: 1,
                }}
              >
                50+
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  color: 'text.secondary',
                  fontWeight: 500,
                }}
              >
                Card Templates
              </Typography>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography
                variant="h3"
                component="div"
                sx={{
                  fontWeight: 'bold',
                  color: 'primary.main',
                  mb: 1,
                }}
              >
                4.9★
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  color: 'text.secondary',
                  fontWeight: 500,
                }}
              >
                User Rating
              </Typography>
            </Grid>
          </Grid>
        </Box>
      </Box>
    </Container>
  );
};

export default Features;
