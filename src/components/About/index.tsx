import { FC } from 'react';
import {
  Avatar,
  Box,
  Card,
  CardContent,
  Container,
  Grid,
  Typography,
} from '@mui/material';
import { Favorite, Groups, Lightbulb, Public } from '@mui/icons-material';

const About: FC = () => {
  const values = [
    {
      icon: <Groups color="primary" sx={{ fontSize: 40 }} />,
      title: 'Community Driven',
      description:
        'Built by Pokemon fans, for Pokemon fans. We listen to our community and continuously improve based on your feedback.',
    },
    {
      icon: <Lightbulb color="primary" sx={{ fontSize: 40 }} />,
      title: 'Innovation First',
      description:
        'We constantly innovate to bring you the latest features and the most authentic Pokemon card creation experience.',
    },
    {
      icon: <Favorite color="primary" sx={{ fontSize: 40 }} />,
      title: 'Passion Project',
      description:
        'Created with love for the Pokemon community. Our goal is to help fans express their creativity through custom cards.',
    },
    {
      icon: <Public color="primary" sx={{ fontSize: 40 }} />,
      title: 'Global Reach',
      description:
        'Serving Pokemon fans worldwide with support for multiple languages and international card formats.',
    },
  ];

  const useCases = [
    {
      title: 'Pokemon Fans',
      description:
        'Create custom Pokemon cards featuring your favorite Pokemon, original characters, or dream team combinations. Perfect for personal collections and sharing with friends.',
    },
    {
      title: 'Content Creators',
      description:
        "Design unique Pokemon cards for your videos, streams, or social media content. Create engaging visuals that stand out and capture your audience's attention.",
    },
    {
      title: 'Educators',
      description:
        'Use our Pokemon card maker for educational projects, teaching materials, or creative assignments. Engage students with interactive Pokemon-themed learning experiences.',
    },
    {
      title: 'Game Designers',
      description:
        'Prototype custom Pokemon cards for fan games, tabletop games, or creative projects. Test new mechanics and designs with professional-quality cards.',
    },
  ];

  return (
    <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
      <Box component="section">
        {/* Section Header */}
        <Box sx={{ textAlign: 'center', mb: 6 }}>
          <Typography
            variant="h2"
            component="h2"
            sx={{
              fontSize: { xs: '2rem', md: '2.5rem' },
              fontWeight: 'bold',
              mb: 2,
              color: 'primary.main',
            }}
          >
            About Pokemon Card Maker
          </Typography>
          <Typography
            variant="h3"
            component="p"
            sx={{
              fontSize: { xs: '1.1rem', md: '1.3rem' },
              color: 'text.secondary',
              maxWidth: 700,
              mx: 'auto',
              lineHeight: 1.6,
            }}
          >
            The premier online Pokemon card creator, empowering fans worldwide
            to design professional-quality custom Pokemon cards
          </Typography>
        </Box>

        {/* Mission Statement */}
        <Box
          sx={{
            mb: 8,
            p: 4,
            background: 'transparent',
            borderRadius: 3,
            border: '1px solid',
            borderColor: 'primary.light',
            textAlign: 'center',
          }}
        >
          <Typography
            variant="h4"
            component="h3"
            sx={{
              fontWeight: 'bold',
              mb: 3,
              color: 'primary.main',
            }}
          >
            Our Mission
          </Typography>
          <Typography
            variant="body1"
            sx={{
              fontSize: '1.1rem',
              color: 'text.primary',
              maxWidth: 800,
              mx: 'auto',
              lineHeight: 1.7,
            }}
          >
            To provide Pokemon fans with the most powerful, user-friendly, and
            authentic Pokemon card creation tool available. We believe every fan
            should have the ability to bring their Pokemon ideas to life with
            professional-quality results, completely free and accessible to
            everyone.
          </Typography>
        </Box>

        {/* Our Values */}
        <Box sx={{ mb: 8 }}>
          <Grid container spacing={4}>
            {values.map((value, index) => (
              <Grid item xs={12} sm={6} key={index}>
                <Card
                  sx={{
                    height: '100%',
                    p: 2,
                    background: 'transparent',
                    border: '1px solid',
                    borderColor: 'divider',
                    borderRadius: 3,
                  }}
                >
                  <CardContent>
                    <Box
                      sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}
                    >
                      <Avatar
                        sx={{
                          bgcolor: 'transparent',
                          border: '2px solid',
                          borderColor: 'primary.light',
                          mr: 2,
                          width: 56,
                          height: 56,
                        }}
                      >
                        {value.icon}
                      </Avatar>
                      <Box>
                        <Typography
                          variant="h6"
                          component="h4"
                          sx={{
                            fontWeight: 'bold',
                            mb: 1,
                            color: 'text.primary',
                          }}
                        >
                          {value.title}
                        </Typography>
                        <Typography
                          variant="body1"
                          sx={{
                            color: 'text.secondary',
                            lineHeight: 1.6,
                          }}
                        >
                          {value.description}
                        </Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Perfect For Section */}
        <Box>
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <Typography
              variant="h4"
              component="h3"
              sx={{
                fontSize: { xs: '1.8rem', md: '2.2rem' },
                fontWeight: 'bold',
                mb: 2,
                color: 'primary.main',
              }}
            >
              Perfect for Pokemon Fans and Creators
            </Typography>
            <Typography
              variant="body1"
              sx={{
                fontSize: { xs: '1rem', md: '1.1rem' },
                color: 'text.secondary',
                maxWidth: 800,
                mx: 'auto',
                lineHeight: 1.6,
              }}
            >
              Discover how our Pokemon card maker serves different communities
              and use cases
            </Typography>
          </Box>
          <Grid container spacing={4}>
            {useCases.map((useCase, index) => (
              <Grid item xs={12} md={6} key={index}>
                <Box
                  sx={{
                    p: 4,
                    height: '100%',
                    background: 'transparent',
                    border: '1px solid',
                    borderColor: 'divider',
                    borderRadius: 3,
                    transition: 'all 0.3s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: theme =>
                        theme.palette.mode === 'dark'
                          ? '0 8px 25px rgba(255, 255, 255, 0.1)'
                          : '0 8px 25px rgba(0, 0, 0, 0.1)',
                    },
                  }}
                >
                  <Typography
                    variant="h6"
                    component="h4"
                    sx={{
                      fontWeight: 'bold',
                      mb: 2,
                      color: 'primary.main',
                      fontSize: { xs: '1.1rem', md: '1.25rem' },
                    }}
                  >
                    For {useCase.title}
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{
                      color: 'text.secondary',
                      lineHeight: 1.7,
                    }}
                  >
                    {useCase.description}
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </Box>
      </Box>
    </Container>
  );
};

export default About;
