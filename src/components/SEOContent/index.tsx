import { FC } from 'react';
import {
  Box,
  Card,
  CardContent,
  Container,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
} from '@mui/material';
import {
  CheckCircle,
  Download,
  Palette,
  Security,
  Speed,
  Star,
} from '@mui/icons-material';

const SEOContent: FC = () => {
  const features = [
    {
      icon: <Palette color="primary" />,
      title: 'Professional Templates',
      description:
        'Choose from Sword & Shield, Scarlet & Violet, and classic Pokemon card designs with authentic styling.',
    },
    {
      icon: <Speed color="primary" />,
      title: 'Easy to Use',
      description:
        'Intuitive interface makes creating custom Pokemon cards simple for beginners and experts alike.',
    },
    {
      icon: <Download color="primary" />,
      title: 'High-Quality Downloads',
      description:
        'Export your custom Pokemon cards in high resolution, perfect for printing or sharing online.',
    },
    {
      icon: <Security color="primary" />,
      title: 'Free & Safe',
      description:
        'Completely free Pokemon card maker with no registration required. Your designs stay private.',
    },
  ];

  const steps = [
    'Choose your Pokemon card template (Sword & Shield, Scarlet & Violet, etc.)',
    'Upload your Pokemon artwork or use our built-in options',
    'Customize card details: name, HP, attacks, abilities, and stats',
    'Add Pokemon types, weakness, resistance, and retreat cost',
    'Preview your custom Pokemon card in real-time',
    'Download your finished Pokemon card in high quality',
  ];

  const cardTypes = [
    'Pokemon V Cards',
    'Pokemon VMAX Cards',
    'Pokemon VSTAR Cards',
    'Pokemon GX Cards',
    'Basic Pokemon Cards',
    'Evolution Pokemon Cards',
    'Trainer Cards',
    'Energy Cards',
  ];

  return (
    <Container maxWidth="lg" sx={{ mt: 6, mb: 6 }}>
      {/* Features Section */}
      <Box component="section" sx={{ mb: 8 }}>
        <Typography
          variant="h3"
          component="h3"
          align="center"
          sx={{ mb: 4, fontWeight: 'bold' }}
        >
          Why Choose Our Pokemon Card Maker?
        </Typography>
        <Grid container spacing={4}>
          {features.map((feature, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card sx={{ height: '100%', textAlign: 'center', p: 2 }}>
                <CardContent>
                  <Box sx={{ mb: 2 }}>{feature.icon}</Box>
                  <Typography
                    variant="h6"
                    component="h4"
                    sx={{ mb: 1, fontWeight: 'bold' }}
                  >
                    {feature.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {feature.description}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* How to Use Section */}
      <Box component="section" sx={{ mb: 8 }}>
        <Typography
          variant="h3"
          component="h3"
          align="center"
          sx={{ mb: 4, fontWeight: 'bold' }}
        >
          How to Create Custom Pokemon Cards
        </Typography>
        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <Typography variant="body1" sx={{ mb: 3, lineHeight: 1.7 }}>
              Our Pokemon card creator makes it easy to design
              professional-looking custom Pokemon cards. Whether you&apos;re
              creating cards for fun, fan projects, or educational purposes, our
              tool provides all the features you need to bring your Pokemon
              ideas to life.
            </Typography>
            <List>
              {steps.map((step, index) => (
                <ListItem key={index} sx={{ pl: 0 }}>
                  <ListItemIcon>
                    <CheckCircle color="primary" />
                  </ListItemIcon>
                  <ListItemText
                    primary={`${index + 1}. ${step}`}
                    sx={{ '& .MuiListItemText-primary': { fontWeight: 500 } }}
                  />
                </ListItem>
              ))}
            </List>
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography
              variant="h4"
              component="h4"
              sx={{ mb: 3, fontWeight: 'bold' }}
            >
              Supported Pokemon Card Types
            </Typography>
            <Typography variant="body1" sx={{ mb: 3, lineHeight: 1.7 }}>
              Create any type of Pokemon card with our comprehensive template
              collection. From modern Sword & Shield designs to classic formats,
              we support all popular Pokemon TCG styles.
            </Typography>
            <Grid container spacing={2}>
              {cardTypes.map((type, index) => (
                <Grid item xs={12} sm={6} key={index}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Star color="primary" sx={{ mr: 1, fontSize: 20 }} />
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {type}
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Grid>
        </Grid>
      </Box>

      {/* Benefits Section */}
      <Box component="section" sx={{ mb: 8 }}>
        <Box sx={{ textAlign: 'center', mb: 6 }}>
          <Typography
            variant="h3"
            component="h3"
            sx={{
              fontSize: { xs: '1.8rem', md: '2.2rem' },
              fontWeight: 'bold',
              mb: 2,
              color: 'primary.main',
            }}
          >
            Perfect for Pokemon Fans and Creators
          </Typography>
          <Typography
            variant="body1"
            sx={{
              fontSize: { xs: '1rem', md: '1.1rem' },
              color: 'text.secondary',
              maxWidth: 600,
              mx: 'auto',
              lineHeight: 1.6,
            }}
          >
            Whether you&apos;re a fan, creator, or educator, our Pokemon card
            maker provides the perfect tools for your needs
          </Typography>
        </Box>
        <Grid container spacing={4}>
          <Grid item xs={12} md={4}>
            <Box
              sx={{
                textAlign: 'center',
                p: 3,
                height: '100%',
                background:
                  'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%)',
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 3,
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 8px 25px rgba(0, 0, 0, 0.1)',
                },
              }}
            >
              <Typography
                variant="h5"
                component="h4"
                sx={{
                  mb: 2,
                  fontWeight: 'bold',
                  color: 'primary.main',
                }}
              >
                For Pokemon Fans
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  lineHeight: 1.7,
                  color: 'text.secondary',
                }}
              >
                Create custom Pokemon cards featuring your favorite Pokemon,
                original characters, or dream team combinations. Perfect for
                personal collections and sharing with friends.
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={4}>
            <Box
              sx={{
                textAlign: 'center',
                p: 3,
                height: '100%',
                background:
                  'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%)',
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 3,
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 8px 25px rgba(0, 0, 0, 0.1)',
                },
              }}
            >
              <Typography
                variant="h5"
                component="h4"
                sx={{
                  mb: 2,
                  fontWeight: 'bold',
                  color: 'primary.main',
                }}
              >
                For Content Creators
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  lineHeight: 1.7,
                  color: 'text.secondary',
                }}
              >
                Design unique Pokemon cards for your videos, streams, or social
                media content. Create engaging visuals that stand out and
                capture your audience&apos;s attention.
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={4}>
            <Box
              sx={{
                textAlign: 'center',
                p: 3,
                height: '100%',
                background:
                  'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%)',
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 3,
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 8px 25px rgba(0, 0, 0, 0.1)',
                },
              }}
            >
              <Typography
                variant="h5"
                component="h4"
                sx={{
                  mb: 2,
                  fontWeight: 'bold',
                  color: 'primary.main',
                }}
              >
                For Educators
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  lineHeight: 1.7,
                  color: 'text.secondary',
                }}
              >
                Use our Pokemon card maker for educational projects, teaching
                materials, or creative assignments. Engage students with
                interactive Pokemon-themed learning experiences.
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Box>
    </Container>
  );
};

export default SEOContent;
