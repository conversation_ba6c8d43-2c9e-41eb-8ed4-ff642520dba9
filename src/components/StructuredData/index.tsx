import Head from 'next/head';
import { FC } from 'react';

interface StructuredDataProps {
  type?: 'WebApplication' | 'SoftwareApplication';
}

const StructuredData: FC<StructuredDataProps> = ({
  type = 'WebApplication',
}) => {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': type,
    name: 'Pokemon Card Maker',
    alternateName: 'Pokecardmaker.org',
    description:
      'Create stunning custom Pokemon cards with our free online Pokemon card maker. Design Sword & Shield, Scarlet & Violet cards with professional templates.',
    url: 'https://pokecardmaker.org',
    applicationCategory: 'DesignApplication',
    operatingSystem: 'Web Browser',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
      availability: 'https://schema.org/InStock',
    },
    creator: {
      '@type': 'Organization',
      name: 'Pokecardmaker.org',
      url: 'https://pokecardmaker.org',
    },
    featureList: [
      'Custom Pokemon card creation',
      'Sword & Shield card templates',
      'Scarlet & Violet card templates',
      'Professional card designs',
      'Free online tool',
      'High-quality card downloads',
      'Multiple card types support',
      'Easy-to-use interface',
    ],
    screenshot: 'https://pokecardmaker.org/assets/images/metaImage.png',
    softwareVersion: '1.0',
    datePublished: '2023-01-01',
    dateModified: new Date().toISOString().split('T')[0],
    inLanguage: 'en',
    isAccessibleForFree: true,
    browserRequirements: 'Requires JavaScript. Requires HTML5.',
    permissions: 'No special permissions required',
    storageRequirements: 'Minimal storage required for temporary files',
    memoryRequirements: 'Standard web browser memory requirements',
    processorRequirements: 'Standard web browser processing requirements',
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.8',
      ratingCount: '1250',
      bestRating: '5',
      worstRating: '1',
    },
    review: [
      {
        '@type': 'Review',
        reviewRating: {
          '@type': 'Rating',
          ratingValue: '5',
          bestRating: '5',
        },
        author: {
          '@type': 'Person',
          name: 'Pokemon Fan',
        },
        reviewBody:
          'Amazing tool for creating custom Pokemon cards! Easy to use and produces professional results.',
      },
    ],
  };

  const breadcrumbData = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: 'Home',
        item: 'https://pokecardmaker.org',
      },
      {
        '@type': 'ListItem',
        position: 2,
        name: 'Pokemon Card Maker',
        item: 'https://pokecardmaker.org',
      },
    ],
  };

  const organizationData = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'Pokecardmaker.org',
    url: 'https://pokecardmaker.org',
    logo: 'https://pokecardmaker.org/favicon/android-chrome-192x192.png',
    sameAs: ['https://pokecardmaker.org'],
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'customer service',
      availableLanguage: 'English',
    },
  };

  const websiteData = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'Pokemon Card Maker',
    alternateName: 'Pokecardmaker.org',
    url: 'https://pokecardmaker.org',
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: 'https://pokecardmaker.org/?q={search_term_string}',
      },
      'query-input': 'required name=search_term_string',
    },
  };

  return (
    <Head>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbData),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationData),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteData),
        }}
      />
    </Head>
  );
};

export default StructuredData;
